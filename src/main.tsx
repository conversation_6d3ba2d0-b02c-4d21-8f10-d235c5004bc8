
import React from 'react';
import { createRoot } from 'react-dom/client';
import { HashRouter } from 'react-router-dom';
import App from './App';
import './index.css';
import AppProviders from './providers/AppProviders';
import { register as registerServiceWorker } from './utils/serviceWorkerRegistration';

// Make React available globally for troubleshooting - helps prevent "React is null" errors
(window as any).React = React;

const container = document.getElementById('root');
if (!container) throw new Error('Failed to find the root element');

const root = createRoot(container);
root.render(
  <React.StrictMode>
    <HashRouter>
      <AppProviders>
        <App />
      </AppProviders>
    </HashRouter>
  </React.StrictMode>
);

// Register service worker for PWA functionality
registerServiceWorker();
